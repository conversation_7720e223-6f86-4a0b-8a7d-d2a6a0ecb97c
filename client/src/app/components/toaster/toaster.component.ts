import { ToasterService } from '@/services/toaster.service';
import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from "@angular/common";
import { IToast } from '@/interfaces/toast';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-toaster',
  imports: [CommonModule],
  templateUrl: './toaster.component.html',
  styleUrl: './toaster.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('toastAnimation', [
      state('entering', style({
        opacity: 0,
        transform: 'translateY(20px) scale(0.95)'
      })),
      state('visible', style({
        opacity: 1,
        transform: 'translateY(0) scale(1)'
      })),
      state('leaving', style({
        opacity: 0,
        transform: 'translateY(-20px) scale(0.95)'
      })),
      transition('entering => visible', [
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)')
      ]),
      transition('visible => leaving', [
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)')
      ])
    ])
  ]
})
export class ToasterComponent implements OnInit {
  toasts$;

  constructor(private toasterService: ToasterService) {
    this.toasts$ = this.toasterService.toasts$;
  }

  ngOnInit(): void { }

  closeToast(toast: IToast): void {
    this.toasterService.removeToast(toast);
  }
}
