<div
  class="toast-container {{toast.position + ' ' + toast.color}}"
  *ngFor="let toast of toasts$ | async"
  [@toastAnimation]="toast.animationState || 'visible'">
    <div class="toast-type-indicator"></div>
    <div class="toast-body">
        <div class="toast-bg-img"></div>
        <div class="toast-message">
            <div class="toast-type-icon"></div>
            <div class="text">{{ toast.message }}</div>
        </div>
        <button class="close-toast-button cursor-pointer" (click)="closeToast(toast)">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L15 15" stroke="#333" stroke-width="1.5" stroke-linecap="round"/>
                <path d="M1 15L15 1" stroke="#333" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
        </button>
    </div>
</div>