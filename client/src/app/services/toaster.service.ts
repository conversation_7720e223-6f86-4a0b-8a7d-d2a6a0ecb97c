import { BehaviorSubject, timer } from 'rxjs';
import { Injectable } from '@angular/core';
import { IToast } from "@/interfaces/toast";

@Injectable({
  providedIn: 'root',
})
export class ToasterService {
  private toastSubject = new BehaviorSubject<IToast[]>([]);
  public toasts$ = this.toastSubject.asObservable();
  private toastIdCounter = 0;

  showToast(message: string, color: string = 'default', position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'bottom-middle' = 'bottom-middle', duration: number = 3000) {
    const newToast: IToast = {
      id: `toast-${++this.toastIdCounter}`,
      message,
      color: color || 'default',
      position: position || 'bottom-middle',
      timer$: timer(duration),
      animationState: 'entering'
    };

    const currentToasts = this.toastSubject.getValue();
    this.toastSubject.next([...currentToasts, newToast]);

    // Set to visible after a short delay to trigger fade-in animation
    setTimeout(() => {
      this.updateToastAnimationState(newToast.id!, 'visible');
    }, 50);

    if (duration > 0) {
      newToast.timer$!.subscribe(() => {
        this.removeToast(newToast);
      });
    }
  }

  removeToast(toastToRemove: IToast) {
    // First set animation state to leaving
    this.updateToastAnimationState(toastToRemove.id!, 'leaving');

    // Remove from array after animation completes
    setTimeout(() => {
      const currentToasts = this.toastSubject.getValue();
      this.toastSubject.next(currentToasts.filter(toast => toast.id !== toastToRemove.id));
    }, 300); // Match this with CSS animation duration
  }

  private updateToastAnimationState(toastId: string, state: 'entering' | 'visible' | 'leaving') {
    const currentToasts = this.toastSubject.getValue();
    const updatedToasts = currentToasts.map(toast =>
      toast.id === toastId ? { ...toast, animationState: state } : toast
    );
    this.toastSubject.next(updatedToasts);
  }
}
